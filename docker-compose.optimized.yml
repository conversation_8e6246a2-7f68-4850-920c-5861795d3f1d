version: '3.8'

services:
  app:
    build: 
      context: .
      dockerfile: Dockerfile.optimized
      target: production
    container_name: dcop_app_optimized
    ports:
      - "8443:8443"
    environment:
      - DATABASE_URL=postgresql://dcop_user:${POSTGRES_PASSWORD:-dcop_password_123}@postgres:5432/dcop_visiteurs
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - dcop_network
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/static/uploads
      - app_cache:/app/cache
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8443/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    read_only: false  # L'app a besoin d'écrire dans /app/logs et /app/cache
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  postgres:
    image: postgres:16-alpine
    container_name: dcop_postgres_optimized
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dcop_password_123}
      POSTGRES_USER: dcop_user
      POSTGRES_DB: dcop_visiteurs
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5434:5432"
    networks:
      - dcop_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dcop_user -d dcop_visiteurs"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
      - DAC_OVERRIDE
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c log_statement=all
      -c log_min_duration_statement=1000
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=16MB
      -c maintenance_work_mem=64MB


  monitoring:
    image: prom/prometheus:latest
    container_name: dcop_monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - dcop_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

networks:
  dcop_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  app_cache:
    driver: local
  prometheus_data:
    driver: local
