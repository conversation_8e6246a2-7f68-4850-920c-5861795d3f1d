-- ===== DONNÉES INITIALES - DCOP GESTION VISITEURS =====
-- Insertion des utilisateurs par défaut

-- ===== UTILISATEUR ADMINISTRATEUR =====
-- Mot de passe: admin123! (sera haché par l'application)
INSERT INTO users (
    id,
    username,
    email,
    hashed_password,
    full_name,
    role,
    status,
    phone,
    department,
    position,
    is_active,
    is_verified,
    created_at,
    updated_at
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    'admin',
    '<EMAIL>',
    'temp_password_will_be_updated',
    'Administrateur DCOP',
    'admin',
    'active',
    '+225 01 02 03 04 05',
    'Direction',
    'Administrateur Système',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- ===== UTILISATEURS DE TEST =====
-- user1: user1 / user123!
INSERT INTO users (
    id,
    username,
    email,
    hashed_password,
    full_name,
    role,
    status,
    phone,
    department,
    position,
    is_active,
    is_verified,
    created_at,
    updated_at,
    created_by
) VALUES (
    '00000000-0000-0000-0000-000000000002',
    'user1',
    '<EMAIL>',
    'temp_password_will_be_updated',
    'Utilisateur Test 1',
    'user',
    'active',
    '+225 01 02 03 04 06',
    'Accueil',
    'Agent d''accueil',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    '00000000-0000-0000-0000-000000000001'
);

-- user2: user2 / user123!
INSERT INTO users (
    id,
    username,
    email,
    hashed_password,
    full_name,
    role,
    status,
    phone,
    department,
    position,
    is_active,
    is_verified,
    created_at,
    updated_at,
    created_by
) VALUES (
    '00000000-0000-0000-0000-000000000003',
    'user2',
    '<EMAIL>',
    'temp_password_will_be_updated',
    'Utilisateur Test 2',
    'user',
    'active',
    '+225 01 02 03 04 07',
    'Sécurité',
    'Agent de sécurité',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    '00000000-0000-0000-0000-000000000001'
);

-- ===== VISITEUR DE TEST =====
INSERT INTO visitors (
    id,
    first_name,
    last_name,
    email,
    phone,
    company,
    position,
    purpose,
    host_name,
    host_department,
    host_email,
    visitor_type,
    status,
    identity_document_type,
    identity_document_number,
    nationality,
    visit_date,
    expected_arrival,
    expected_departure,
    notes,
    created_at,
    updated_at,
    created_by
) VALUES (
    uuid_generate_v4(),
    'Jean',
    'Dupont',
    '<EMAIL>',
    '+225 07 08 09 10 11',
    'Entreprise ABC',
    'Directeur Commercial',
    'Réunion d''affaires',
    'Marie Martin',
    'Direction Commerciale',
    '<EMAIL>',
    'business',
    'approved',
    'CNI',
    'CI123456789',
    'Ivoirienne',
    CURRENT_DATE,
    CURRENT_TIMESTAMP + INTERVAL '2 hours',
    CURRENT_TIMESTAMP + INTERVAL '4 hours',
    'Visiteur de test',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    '00000000-0000-0000-0000-000000000001'
);

-- ===== CONFIRMATION =====
DO $$
BEGIN
    RAISE NOTICE '✅ Données initiales créées:';
    RAISE NOTICE '   - % utilisateurs créés', (SELECT COUNT(*) FROM users);
    RAISE NOTICE '   - % visiteurs de test créés', (SELECT COUNT(*) FROM visitors);
    RAISE NOTICE '   - Identifiants: admin/admin123!, user1/user123!, user2/user123!';
END $$;
