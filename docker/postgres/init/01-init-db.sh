#!/bin/bash
set -e

# Activation des extensions PostgreSQL nécessaires
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Extensions de sécurité
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    CREATE EXTENSION IF NOT EXISTS "citext";

    -- Configuration des paramètres de sécurité
    ALTER SYSTEM SET ssl TO 'on';
    ALTER SYSTEM SET ssl_ciphers TO 'HIGH:!aNULL:!MD5';
    
    -- Création des types enum s'ils n'existent pas
    DO \$\$
    BEGIN
        -- Type pour les rôles utilisateur
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
            CREATE TYPE user_role AS ENUM ('admin', 'directeur', 'gestionnaire', 'agent');
        END IF;

        -- Type pour le statut de chiffrement
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'encryption_status') THEN
            CREATE TYPE encryption_status AS ENUM ('encrypted', 'plaintext', 'failed');
        END IF;

        -- Type pour le statut des visiteurs
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visitor_status') THEN
            CREATE TYPE visitor_status AS ENUM ('pending', 'approved', 'rejected', 'present', 'received', 'departed');
        END IF;
    END
    \$\$;

    -- Configuration des privilèges par défaut
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO $POSTGRES_USER;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO $POSTGRES_USER;
EOSQL
