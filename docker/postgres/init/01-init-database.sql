-- ===== INITIALISATION BASE DE DONNÉES DCOP - GESTION VISITEURS =====
-- Script d'initialisation sécurisé pour PostgreSQL 16 (sans SSL)

-- ===== ACTIVATION DES EXTENSIONS DE SÉCURITÉ =====
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "citext";

-- ===== CRÉATION DES TYPES ENUM =====
CREATE TYPE user_role AS ENUM ('admin', 'user', 'receptionist', 'security', 'manager');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending');
CREATE TYPE visitor_status AS ENUM ('pending', 'approved', 'rejected', 'present', 'departed', 'expired');
CREATE TYPE visitor_type AS ENUM ('business', 'personal', 'official', 'maintenance', 'delivery', 'interview', 'meeting');

-- ===== TABLE USERS =====
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username CITEXT UNIQUE NOT NULL,
    email CITEXT UNIQUE NOT NULL,
    hashed_password TEXT NOT NULL,
    full_name TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'user',
    status user_status NOT NULL DEFAULT 'active',
    phone TEXT,
    department TEXT,
    position TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_verified BOOLEAN NOT NULL DEFAULT false,
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    last_login_attempt TIMESTAMP WITH TIME ZONE,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- ===== TABLE VISITORS =====
CREATE TABLE visitors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email CITEXT,
    phone TEXT,
    company TEXT,
    position TEXT,
    purpose TEXT NOT NULL,
    host_name TEXT NOT NULL,
    host_department TEXT,
    host_phone TEXT,
    host_email CITEXT,
    visitor_type visitor_type NOT NULL DEFAULT 'business',
    status visitor_status NOT NULL DEFAULT 'pending',
    identity_document_type TEXT,
    identity_document_number TEXT,
    nationality TEXT,
    address TEXT,
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    visit_date DATE NOT NULL,
    expected_arrival TIMESTAMP WITH TIME ZONE,
    expected_departure TIMESTAMP WITH TIME ZONE,
    actual_arrival TIMESTAMP WITH TIME ZONE,
    actual_departure TIMESTAMP WITH TIME ZONE,
    badge_number TEXT,
    badge_issued_at TIMESTAMP WITH TIME ZONE,
    badge_returned_at TIMESTAMP WITH TIME ZONE,
    photo_path TEXT,
    notes TEXT,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejected_by UUID REFERENCES users(id),
    rejected_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    check_in_by UUID REFERENCES users(id),
    check_out_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id)
);

-- ===== INDEXES POUR PERFORMANCE =====
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

CREATE INDEX idx_visitors_status ON visitors(status);
CREATE INDEX idx_visitors_visit_date ON visitors(visit_date);
CREATE INDEX idx_visitors_host_name ON visitors(host_name);
CREATE INDEX idx_visitors_company ON visitors(company);
CREATE INDEX idx_visitors_created_at ON visitors(created_at);
CREATE INDEX idx_visitors_email ON visitors(email);

-- ===== TRIGGERS POUR UPDATED_AT =====
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_visitors_updated_at BEFORE UPDATE ON visitors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===== PERMISSIONS =====
GRANT USAGE ON SCHEMA public TO dcop_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO dcop_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO dcop_user;

-- ===== COMMENTAIRES =====
COMMENT ON TABLE users IS 'Table des utilisateurs du système';
COMMENT ON TABLE visitors IS 'Table des visiteurs avec informations complètes';

-- ===== CONFIRMATION =====
DO $$
BEGIN
    RAISE NOTICE '✅ Base de données DCOP initialisée avec succès';
    RAISE NOTICE '   - Tables créées: users, visitors';
    RAISE NOTICE '   - Extensions activées: uuid-ossp, pgcrypto, citext';
    RAISE NOTICE '   - Permissions accordées à dcop_user';
END $$;
